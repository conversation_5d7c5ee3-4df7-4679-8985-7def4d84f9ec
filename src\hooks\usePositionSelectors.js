/**
 * 职位选择器管理Hook
 *
 * 封装考试类型、地区、具体考试等选择器的状态管理和交互逻辑，
 * 以及职位查询中使用的地区选择器及其联动逻辑
 *
 * @param {Object} options - 配置选项
 * @param {Function} options.getConfigApi - 获取配置数据的API函数
 * @param {Function} options.getRegionTreeApi - 获取地区树数据的API函数
 * @param {Function} options.getProjectListApi - 获取项目列表的API函数
 * @param {Function} options.getRegionListApi - 获取地区列表的API函数（用于职位地区选择器）
 * @param {string} options.source - 数据源标识，如 'apply' 或 'enter'
 * @param {Object} options.routeParams - 路由参数对象，包含 project_id 等
 * @param {Ref} options.articleId - 文章ID的响应式引用（用于职位地区选择器）
 *
 * @returns {Object} 返回选择器相关的状态和方法
 */

import { ref, reactive, computed, nextTick } from "vue"

export function usePositionSelectors(options = {}) {
  const {
    getConfigApi,
    getRegionTreeApi,
    getProjectListApi,
    getRegionListApi,
    source = "apply",
    routeParams = {},
    articleId,
  } = options

  // ==================== 顶部选择器组状态 ====================

  // 考试类型选择器
  const examTypeVisible = ref(false)
  const examTypeOptions = ref([])
  const selectedExamType = ref("")
  const selectedExamTypeKey = ref("")
  const selectedExamValues = ref([])

  // 地区选择器
  const regionVisible = ref(false)
  const regionOptions = ref([])
  const selectedRegion = ref("")
  const selectedRegionKey = ref("")
  const selectedRegionValues = ref([])

  // 具体考试选择器
  const specificExamVisible = ref(false)
  const specificExamOptions = ref([])
  const selectedSpecificExam = ref("")
  const projectName = ref("")
  const projectId = ref(null)

  // 全局配置数据
  const globalConfig = ref(null)
  const projectList = ref([])

  // ==================== 职位地区选择器状态 ====================

  // 地区数据缓存
  const regionDataCache = ref({})

  // 地区选择器状态
  const positionRegionSelector = reactive({
    level: 1, // 1=三级联动, 2=市区二级, 3=只选区, 4=不可编辑
    editable: true,
    title: "请选择地区",
    selectedCodes: [],
    selectedValues: [], // [省id, 市id, 区id]
    displayText: "全部地区",
  })

  // 地区选择器列数据
  const positionRegionColumns = ref([])

  // 地区选择器弹窗状态
  const showSearchRegionPopup = ref(false)

  // ==================== 计算属性 ====================
  const selectedExamTypeText = computed(() => selectedExamType.value)
  const selectedRegionText = computed(() => selectedRegion.value)

  // ==================== 顶部选择器数据获取方法 ====================

  /**
   * 获取全局配置数据
   */
  const getConfig = async () => {
    if (!getConfigApi) {
      console.warn("getConfigApi not provided")
      return
    }

    try {
      const res = await getConfigApi()
      if (res?.data) {
        globalConfig.value = res.data

        // 设置考试类型选项
        if (res.data.examtype_list) {
          examTypeOptions.value = res.data.examtype_list.map((item) => ({
            text: item.type_name,
            value: item.id,
          }))
        }

        // 设置地区选项
        if (res.data.province_list) {
          regionOptions.value = res.data.province_list.map((item) => ({
            text: item.name,
            value: item.key,
          }))
        }
      }
    } catch (error) {
      console.error("获取配置失败:", error)
    }
  }

  /**
   * 获取地区树数据
   */
  const getRegionTree = async () => {
    if (!getRegionTreeApi) {
      console.warn("getRegionTreeApi not provided")
      return
    }

    try {
      const res = await getRegionTreeApi({ is_ignore: 0 })
      if (res?.data) {
        regionOptions.value = res.data.map((item) => ({
          text: item.area_name,
          value: item.id,
        }))
      }
    } catch (error) {
      console.error("获取地区树失败:", error)
    }
  }

  /**
   * 获取考试项目列表
   */
  const getProjectList = async () => {
    if (!getProjectListApi) {
      console.warn("getProjectListApi not provided")
      return
    }

    try {
      const param = {
        province: selectedRegionKey.value,
        exam_type: selectedExamTypeKey.value,
        source: source,
      }

      const res = await getProjectListApi(param)
      if (res?.data) {
        projectList.value = res.data

        // 更新具体考试选项
        specificExamOptions.value = res.data.map((item) => ({
          text: item.name,
          value: item.id,
        }))

        // 处理默认选中项
        const targetProjectId = projectId.value || routeParams.project_id
        let selectedProject = null

        if (targetProjectId) {
          selectedProject = res.data.find((item) => item.id == targetProjectId)
          if (!selectedProject) {
            console.warn(`路由中指定的project_id(${targetProjectId})未找到，使用默认第一个项目`)
            selectedProject = res.data[0]
          }
        } else {
          selectedProject = res.data[0]
        }

        if (selectedProject) {
          projectId.value = selectedProject.id
          projectName.value = selectedProject.name
        }

        return selectedProject
      }
    } catch (error) {
      console.error("获取项目列表失败:", error)
    }
  }

  // ==================== 职位地区选择器数据获取方法 ====================

  /**
   * 带缓存的地区数据获取
   * @param {string|number} parentId - 父级地区ID
   * @param {number} dataLevel - 数据级别 (1=省, 2=市, 3=区)
   * @returns {Promise<Array>} 地区数据数组
   */
  const getRegionDataWithCache = async (parentId, dataLevel = null) => {
    const cacheKey = `${parentId}_${dataLevel}`

    if (regionDataCache.value[cacheKey]) {
      return regionDataCache.value[cacheKey]
    }

    const data = await getRegionData(parentId, dataLevel)
    regionDataCache.value[cacheKey] = data
    return data
  }

  /**
   * 获取地区数据
   * @param {string|number} parentId - 父级地区ID
   * @param {number} dataLevel - 数据级别
   * @returns {Promise<Array>} 地区数据数组
   */
  const getRegionData = async (parentId, dataLevel) => {
    if (!getRegionListApi || !articleId?.value) {
      console.warn("getRegionListApi or articleId not provided")
      return []
    }

    const param = {
      article_id: articleId.value,
      level: dataLevel,
      parent_id: parentId,
    }

    try {
      const res = await getRegionListApi(param)
      return (
        res?.data.map((item) => ({
          text: item.area_name || item.name,
          value: item.id,
        })) || []
      )
    } catch (error) {
      console.error("获取地区数据失败:", error)
      return []
    }
  }

  // ==================== 顶部选择器交互方法 ====================

  /**
   * 考试类型选择器点击事件
   */
  const onExamTypeClick = () => {
    selectedExamValues.value = [selectedExamTypeKey.value]
    examTypeVisible.value = true
  }

  /**
   * 地区选择器点击事件
   */
  const onRegionClick = () => {
    selectedRegionValues.value = [selectedRegionKey.value]
    regionVisible.value = true
  }

  /**
   * 具体考试选择器点击事件
   */
  const onSpecificExamClick = () => {
    specificExamVisible.value = true
  }

  /**
   * 考试类型选择确认
   * @param {Object} param - 选择结果
   * @param {Array} param.selectedOptions - 选中的选项
   */
  const onExamTypeConfirm = ({ selectedOptions }) => {
    if (selectedOptions && selectedOptions.length > 0) {
      selectedExamType.value = selectedOptions[0].text
      selectedExamTypeKey.value = selectedExamValues.value[0]
      examTypeVisible.value = false

      // 重置项目相关数据
      projectId.value = null
      projectName.value = ""

      // 触发项目列表更新
      return getProjectList()
    }
  }

  /**
   * 地区选择确认
   * @param {Object} param - 选择结果
   * @param {Array} param.selectedOptions - 选中的选项
   */
  const onRegionConfirm = ({ selectedOptions }) => {
    if (selectedOptions && selectedOptions.length > 0) {
      selectedRegion.value = selectedOptions[0].text
      selectedRegionKey.value = selectedRegionValues.value[0]

      // 重置项目相关数据
      projectId.value = null
      projectName.value = ""

      // 触发项目列表更新
      return getProjectList()
    }
    regionVisible.value = false
  }

  /**
   * 具体考试选择确认
   * @param {Object} param - 选择结果
   * @param {Array} param.selectedOptions - 选中的选项
   */
  const onSpecificExamConfirm = ({ selectedOptions }) => {
    if (selectedOptions && selectedOptions.length > 0) {
      selectedSpecificExam.value = selectedOptions[0].text
      projectId.value = selectedOptions[0].value
      projectName.value = selectedOptions[0].text
      specificExamVisible.value = false

      return {
        projectId: projectId.value,
        projectName: projectName.value,
      }
    }
  }

  /**
   * 从API响应数据中设置选择器状态
   * @param {Object} data - API响应数据
   */
  const setSelectorsFromApiData = (data) => {
    // 设置考试类型
    if (data.examtype_list && data.examtype_list.length) {
      examTypeOptions.value = data.examtype_list.map((item) => ({
        text: item.type_name,
        value: item.id,
      }))

      if (data.exam_type) {
        const examTypeData = examTypeOptions.value.find((item) => item.value == data.exam_type)
        if (examTypeData) {
          selectedExamType.value = examTypeData.text
          selectedExamTypeKey.value = examTypeData.value
        }
      }
    }

    // 设置地区选项
    if (data.province_list && data.province_list.length) {
      regionOptions.value = data.province_list.map((item) => ({
        text: item.area_name,
        value: item.id,
      }))

      if (data.region_province) {
        const regionData = regionOptions.value.find((item) => item.value == data.region_province)
        if (regionData) {
          selectedRegion.value = regionData.text
          selectedRegionKey.value = regionData.value
        }
      }
    }

    // 设置项目信息
    if (data.project_list && data.project_list.length) {
      specificExamOptions.value = data.project_list.map((item) => ({
        text: item.name,
        value: item.id,
      }))

      const targetProjectId = routeParams.project_id
      if (targetProjectId) {
        const selectedProject = specificExamOptions.value.find(
          (item) => item.value == targetProjectId
        )
        if (selectedProject) {
          projectId.value = selectedProject.value
          projectName.value = selectedProject.text
        }
      }
    }
  }

  // ==================== 职位地区选择器设置和联动逻辑 ====================

  /**
   * 设置职位地区选择器状态
   * 根据不同的level配置选择器的行为和数据
   * @param {Object} data - 包含地区配置信息的数据对象
   */
  const setupPositionRegionSelector = async (data) => {
    const level = data.level || 1
    positionRegionSelector.level = level

    switch (level) {
      case 4:
        // level=4: 不可编辑，需要显示具体的省-市-区文案
        positionRegionSelector.editable = false
        await setupLevel4Selector(data)
        break
      case 3:
        // level=3: 只能选择区县
        positionRegionSelector.editable = true
        positionRegionSelector.title = "请选择区县"
        await setupLevel3Selector(data)
        break
      case 2:
        // level=2: 可选择市区
        positionRegionSelector.editable = true
        positionRegionSelector.title = "请选择市区"
        await setupLevel2Selector(data)
        break
      case 1:
      default:
        // level=1: 完整三级联动
        positionRegionSelector.editable = true
        positionRegionSelector.title = "请选择地区"
        await setupLevel1Selector()
        break
    }
  }

  /**
   * 设置Level 4选择器（不可编辑）
   */
  const setupLevel4Selector = async (data) => {
    if (data.region_province && data.region_city && data.region_district) {
      try {
        // 获取省份数据
        const provinces = await getRegionDataWithCache(0, 1)
        const province = provinces?.find((p) => p.value == data.region_province)

        // 获取城市数据
        const cities = await getRegionDataWithCache(data.region_province, 2)
        const city = cities?.find((c) => c.value == data.region_city)

        // 获取区县数据
        const districts = await getRegionDataWithCache(data.region_city, 3)
        const district = districts?.find((d) => d.value == data.region_district)

        if (province && city && district) {
          positionRegionSelector.displayText = `${province.text}-${city.text}-${district.text}`
        } else {
          positionRegionSelector.displayText = "全部地区"
        }

        // 设置选中值（虽然不可编辑，但保持数据一致性）
        positionRegionSelector.selectedValues = [
          data.region_province,
          data.region_city,
          data.region_district,
        ]
      } catch (error) {
        console.error("获取地区显示文案失败:", error)
        positionRegionSelector.displayText = "全部地区"
      }
    } else {
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 设置Level 3选择器（只选区县）
   */
  const setupLevel3Selector = async (data) => {
    if (data.region_city) {
      await loadDistrictData(data.region_city)
      if (positionRegionColumns.value[0] && positionRegionColumns.value[0].length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = [data.region_province, data.region_city, ""]
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
    } else {
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 设置Level 2选择器（市区二级）
   */
  const setupLevel2Selector = async (data) => {
    try {
      const cities = await getRegionDataWithCache(data.region_province, 2)
      const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
      positionRegionColumns.value = [cities, districts]

      if (cities.length > 0 && districts.length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = [data.region_province, "", ""]
      } else if (cities.length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = [data.region_province, ""]
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
    } catch (error) {
      console.error("预加载市区数据失败:", error)
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 设置Level 1选择器（三级联动）
   */
  const setupLevel1Selector = async () => {
    try {
      const provinces = await getRegionDataWithCache(0, 1)
      const cities = provinces.length ? await getRegionDataWithCache(provinces[0].value, 2) : []
      const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
      positionRegionColumns.value = [provinces, cities, districts]

      if (provinces.length > 0 && cities.length > 0 && districts.length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = ["", "", ""]
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
    } catch (error) {
      console.error("预加载三级联动数据失败:", error)
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 加载区县数据
   * @param {string|number} cityId - 城市ID
   */
  const loadDistrictData = async (cityId) => {
    try {
      const districts = await getRegionData(cityId, 3)
      positionRegionColumns.value = [districts]
    } catch (error) {
      console.error("获取区县数据失败:", error)
    }
  }

  /**
   * 职位地区选择器联动事件
   * @param {Array} selectedValues - 选中的值数组
   * @param {number} columnIndex - 当前操作的列索引
   */
  const onPositionRegionPickerChange = async (selectedValues, columnIndex) => {
    positionRegionSelector.selectedValues = [...selectedValues]

    if (positionRegionSelector.level === 1) {
      // 三级联动模式
      if (columnIndex === 0) {
        // 选择了省份，需要更新市和区
        const provinceId = selectedValues[0]
        if (provinceId) {
          const cities = await getRegionDataWithCache(provinceId, 2)
          const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
          positionRegionColumns.value = [positionRegionColumns.value[0], cities, districts]

          // 重置市区选择
          positionRegionSelector.selectedValues = [provinceId, "", ""]
        }
      } else if (columnIndex === 1) {
        // 选择了市，需要更新区
        const cityId = selectedValues[1]
        if (cityId) {
          const districts = await getRegionDataWithCache(cityId, 3)
          positionRegionColumns.value = [
            positionRegionColumns.value[0],
            positionRegionColumns.value[1],
            districts,
          ]

          // 重置区选择
          positionRegionSelector.selectedValues = [selectedValues[0], cityId, ""]
        }
      }
    } else if (positionRegionSelector.level === 2) {
      // 市区二级联动模式
      if (columnIndex === 0) {
        // 选择了市，需要更新区
        const cityId = selectedValues[0]
        if (cityId) {
          const districts = await getRegionDataWithCache(cityId, 3)
          positionRegionColumns.value = [positionRegionColumns.value[0], districts]

          // 重置区选择
          positionRegionSelector.selectedValues = [cityId, ""]
        }
      }
    }
  }

  /**
   * 职位地区选择器确认事件
   * @param {Array} selectedValues - 选中的值数组
   */
  const onPositionRegionPickerConfirm = (selectedValues) => {
    positionRegionSelector.selectedValues = [...selectedValues]

    // 根据level生成显示文本
    let displayText = "全部地区"

    if (positionRegionSelector.level === 1) {
      // 三级联动：省-市-区
      const texts = []
      selectedValues.forEach((value, index) => {
        if (value && positionRegionColumns.value[index]) {
          const item = positionRegionColumns.value[index].find((col) => col.value === value)
          if (item) texts.push(item.text)
        }
      })
      if (texts.length > 0) {
        displayText = texts.join("-")
      }
    } else if (positionRegionSelector.level === 2) {
      // 市区二级：市-区
      const texts = []
      selectedValues.forEach((value, index) => {
        if (value && positionRegionColumns.value[index]) {
          const item = positionRegionColumns.value[index].find((col) => col.value === value)
          if (item) texts.push(item.text)
        }
      })
      if (texts.length > 0) {
        displayText = texts.join("-")
      }
    } else if (positionRegionSelector.level === 3) {
      // 只选区县
      if (selectedValues[0] && positionRegionColumns.value[0]) {
        const item = positionRegionColumns.value[0].find((col) => col.value === selectedValues[0])
        if (item) displayText = item.text
      }
    }

    positionRegionSelector.displayText = displayText
    showSearchRegionPopup.value = false

    return {
      selectedValues: [...selectedValues],
      displayText,
    }
  }

  /**
   * 打开职位地区选择器
   */
  const openPositionRegionSelector = () => {
    if (positionRegionSelector.editable) {
      showSearchRegionPopup.value = true
    }
  }

  /**
   * 重置所有选择器状态
   */
  const resetAllSelectors = () => {
    // 重置顶部选择器
    selectedExamType.value = ""
    selectedExamTypeKey.value = ""
    selectedRegion.value = ""
    selectedRegionKey.value = ""
    selectedSpecificExam.value = ""
    projectName.value = ""
    projectId.value = null

    // 重置职位地区选择器
    positionRegionSelector.selectedValues = []
    positionRegionSelector.displayText = "全部地区"

    // 清空选项数据
    examTypeOptions.value = []
    regionOptions.value = []
    specificExamOptions.value = []
    positionRegionColumns.value = []
  }

  // ==================== 返回值 ====================
  return {
    // 顶部选择器状态
    examTypeVisible,
    examTypeOptions,
    selectedExamType,
    selectedExamTypeKey,
    selectedExamValues,
    selectedExamTypeText,

    regionVisible,
    regionOptions,
    selectedRegion,
    selectedRegionKey,
    selectedRegionValues,
    selectedRegionText,

    specificExamVisible,
    specificExamOptions,
    selectedSpecificExam,
    projectName,
    projectId,

    // 全局数据
    globalConfig,
    projectList,

    // 职位地区选择器状态
    positionRegionSelector,
    positionRegionColumns,
    showSearchRegionPopup,
    regionDataCache,

    // 数据获取方法
    getConfig,
    getRegionTree,
    getProjectList,
    getRegionDataWithCache,
    getRegionData,

    // 顶部选择器交互方法
    onExamTypeClick,
    onRegionClick,
    onSpecificExamClick,
    onExamTypeConfirm,
    onRegionConfirm,
    onSpecificExamConfirm,

    // 职位地区选择器方法
    setupPositionRegionSelector,
    onPositionRegionPickerChange,
    onPositionRegionPickerConfirm,
    openPositionRegionSelector,

    // 工具方法
    setSelectorsFromApiData,
    resetAllSelectors,
  }
}
