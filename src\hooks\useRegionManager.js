/**
 * 地区数据管理Hook
 * 
 * 封装地区数据缓存、联动选择器和地区相关的状态管理逻辑
 * 支持多级地区联动（省-市-区）和不同level的地区选择模式
 * 
 * @param {Object} options - 配置选项
 * @param {Function} options.getRegionListApi - 获取地区列表的API函数
 * @param {Ref} options.articleId - 文章ID的响应式引用
 * 
 * @returns {Object} 返回地区管理相关的状态和方法
 */

import { ref, reactive, nextTick } from 'vue'

export function useRegionManager(options = {}) {
  const {
    getRegionListApi,
    articleId
  } = options

  // ==================== 地区数据缓存 ====================
  const regionDataCache = ref({})

  // ==================== 地区选择器状态 ====================
  const positionRegionSelector = reactive({
    level: 1, // 1=三级联动, 2=市区二级, 3=只选区, 4=不可编辑
    editable: true,
    title: "请选择地区",
    selectedCodes: [],
    selectedValues: [], // [省id, 市id, 区id]
    displayText: "全部地区",
  })

  // 地区选择器列数据
  const positionRegionColumns = ref([])

  // 地区选择器弹窗状态
  const showSearchRegionPopup = ref(false)

  // ==================== 地区数据获取和缓存 ====================
  
  /**
   * 带缓存的地区数据获取
   * @param {string|number} parentId - 父级地区ID
   * @param {number} dataLevel - 数据级别 (1=省, 2=市, 3=区)
   * @returns {Promise<Array>} 地区数据数组
   */
  const getRegionDataWithCache = async (parentId, dataLevel = null) => {
    const cacheKey = `${parentId}_${dataLevel}`
    
    if (regionDataCache.value[cacheKey]) {
      return regionDataCache.value[cacheKey]
    }
    
    const data = await getRegionData(parentId, dataLevel)
    regionDataCache.value[cacheKey] = data
    return data
  }

  /**
   * 获取地区数据
   * @param {string|number} parentId - 父级地区ID
   * @param {number} dataLevel - 数据级别
   * @returns {Promise<Array>} 地区数据数组
   */
  const getRegionData = async (parentId, dataLevel) => {
    if (!getRegionListApi || !articleId?.value) {
      console.warn('getRegionListApi or articleId not provided')
      return []
    }

    const param = {
      article_id: articleId.value,
      level: dataLevel,
      parent_id: parentId,
    }

    try {
      const res = await getRegionListApi(param)
      return (
        res?.data.map((item) => ({
          text: item.area_name || item.name,
          value: item.id,
        })) || []
      )
    } catch (error) {
      console.error("获取地区数据失败:", error)
      return []
    }
  }

  // ==================== 地区选择器设置 ====================
  
  /**
   * 设置地区选择器状态
   * 根据不同的level配置选择器的行为和数据
   * @param {Object} data - 包含地区配置信息的数据对象
   */
  const setupPositionRegionSelector = async (data) => {
    const level = data.level || 1
    positionRegionSelector.level = level

    switch (level) {
      case 4:
        // level=4: 不可编辑，需要显示具体的省-市-区文案
        positionRegionSelector.editable = false
        await setupLevel4Selector(data)
        break
      case 3:
        // level=3: 只能选择区县
        positionRegionSelector.editable = true
        positionRegionSelector.title = "请选择区县"
        await setupLevel3Selector(data)
        break
      case 2:
        // level=2: 可选择市区
        positionRegionSelector.editable = true
        positionRegionSelector.title = "请选择市区"
        await setupLevel2Selector(data)
        break
      case 1:
      default:
        // level=1: 完整三级联动
        positionRegionSelector.editable = true
        positionRegionSelector.title = "请选择地区"
        await setupLevel1Selector()
        break
    }
  }

  /**
   * 设置Level 4选择器（不可编辑）
   */
  const setupLevel4Selector = async (data) => {
    if (data.region_province && data.region_city && data.region_district) {
      try {
        // 获取省份数据
        const provinces = await getRegionDataWithCache(0, 1)
        const province = provinces?.find((p) => p.value == data.region_province)

        // 获取城市数据
        const cities = await getRegionDataWithCache(data.region_province, 2)
        const city = cities?.find((c) => c.value == data.region_city)

        // 获取区县数据
        const districts = await getRegionDataWithCache(data.region_city, 3)
        const district = districts?.find((d) => d.value == data.region_district)

        if (province && city && district) {
          positionRegionSelector.displayText = `${province.text}-${city.text}-${district.text}`
        } else {
          positionRegionSelector.displayText = "全部地区"
        }

        // 设置选中值（虽然不可编辑，但保持数据一致性）
        positionRegionSelector.selectedValues = [
          data.region_province,
          data.region_city,
          data.region_district,
        ]
      } catch (error) {
        console.error("获取地区显示文案失败:", error)
        positionRegionSelector.displayText = "全部地区"
      }
    } else {
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 设置Level 3选择器（只选区县）
   */
  const setupLevel3Selector = async (data) => {
    if (data.region_city) {
      await loadDistrictData(data.region_city)
      if (positionRegionColumns.value[0] && positionRegionColumns.value[0].length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = [data.region_province, data.region_city, ""]
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
    } else {
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 设置Level 2选择器（市区二级）
   */
  const setupLevel2Selector = async (data) => {
    try {
      const cities = await getRegionDataWithCache(data.region_province, 2)
      const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
      positionRegionColumns.value = [cities, districts]

      if (cities.length > 0 && districts.length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = [data.region_province, "", ""]
      } else if (cities.length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = [data.region_province, ""]
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
    } catch (error) {
      console.error("预加载市区数据失败:", error)
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 设置Level 1选择器（三级联动）
   */
  const setupLevel1Selector = async () => {
    try {
      const provinces = await getRegionDataWithCache(0, 1)
      const cities = provinces.length ? await getRegionDataWithCache(provinces[0].value, 2) : []
      const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
      positionRegionColumns.value = [provinces, cities, districts]

      if (provinces.length > 0 && cities.length > 0 && districts.length > 0) {
        positionRegionSelector.displayText = "全部地区"
        positionRegionSelector.selectedValues = ["", "", ""]
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
    } catch (error) {
      console.error("预加载三级联动数据失败:", error)
      positionRegionSelector.displayText = "全部地区"
    }
  }

  /**
   * 加载区县数据
   * @param {string|number} cityId - 城市ID
   */
  const loadDistrictData = async (cityId) => {
    try {
      const districts = await getRegionData(cityId, 3)
      positionRegionColumns.value = [districts]
    } catch (error) {
      console.error("获取区县数据失败:", error)
    }
  }
