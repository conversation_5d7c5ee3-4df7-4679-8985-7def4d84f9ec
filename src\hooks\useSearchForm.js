/**
 * 搜索表单管理Hook
 * 
 * 封装搜索表单的状态管理和交互逻辑，包括职位搜索、单位搜索等功能
 * 适用于需要搜索功能的考试相关页面
 * 
 * @param {Object} options - 配置选项
 * @param {Function} options.onSearch - 搜索回调函数，接收搜索参数
 * @param {Function} options.getWorkListApi - 获取用人单位列表的API函数
 * @param {Ref} options.articleId - 文章ID的响应式引用
 * 
 * @returns {Object} 返回搜索表单相关的状态和方法
 */

import { ref, computed } from 'vue'

export function useSearchForm(options = {}) {
  const {
    onSearch,
    getWorkListApi,
    articleId
  } = options

  // ==================== 搜索表单状态 ====================
  
  // 职位搜索输入
  const positionInput = ref("")
  
  // 用人单位选择
  const selectedSearchUnitText = ref("全部单位")
  const selectedSearchUnitKey = ref("")
  const searchUnitOptions = ref([])
  const searchUnitVisible = ref(false)
  const selectedSearchUnitValues = ref([])
  
  // 搜索状态
  const hasSearched = ref(false)
  const isSearching = ref(false)
  
  // 排序状态（仅部分页面需要）
  const sortOrder = ref("desc") // desc: 降序, asc: 升序
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否有搜索条件
   */
  const hasSearchConditions = computed(() => {
    return positionInput.value.trim() !== "" || selectedSearchUnitKey.value !== ""
  })
  
  /**
   * 搜索参数对象
   */
  const searchParams = computed(() => {
    const params = {}
    
    if (positionInput.value.trim()) {
      params.position_name = positionInput.value.trim()
    }
    
    if (selectedSearchUnitKey.value) {
      params.work_id = selectedSearchUnitKey.value
    }
    
    return params
  })

  // ==================== 数据获取方法 ====================
  
  /**
   * 获取用人单位列表
   */
  const getWorkList = async () => {
    if (!getWorkListApi || !articleId?.value) {
      console.warn('getWorkListApi or articleId not provided')
      return
    }

    try {
      const res = await getWorkListApi({ article_id: articleId.value })
      if (res?.data) {
        searchUnitOptions.value = res.data.map((item) => ({
          text: item.work_name,
          value: item.id,
        }))
      }
    } catch (error) {
      console.error("获取用人单位列表失败:", error)
    }
  }

  // ==================== 搜索交互方法 ====================
  
  /**
   * 执行搜索
   * @param {Object} additionalParams - 额外的搜索参数
   */
  const executeSearch = async (additionalParams = {}) => {
    if (isSearching.value) return
    
    isSearching.value = true
    hasSearched.value = true
    
    try {
      const params = {
        ...searchParams.value,
        ...additionalParams
      }
      
      if (onSearch) {
        await onSearch(params)
      }
    } catch (error) {
      console.error("搜索失败:", error)
    } finally {
      isSearching.value = false
    }
  }
  
  /**
   * 搜索按钮点击事件
   */
  const onSearchClick = async () => {
    await executeSearch()
  }
  
  /**
   * 清除搜索条件
   */
  const onClearSearch = () => {
    positionInput.value = ""
    selectedSearchUnitText.value = "全部单位"
    selectedSearchUnitKey.value = ""
    selectedSearchUnitValues.value = []
    hasSearched.value = false
    
    // 执行清空后的搜索
    executeSearch()
  }
  
  /**
   * 重置搜索表单
   */
  const resetSearchForm = () => {
    positionInput.value = ""
    selectedSearchUnitText.value = "全部单位"
    selectedSearchUnitKey.value = ""
    selectedSearchUnitValues.value = []
    hasSearched.value = false
    isSearching.value = false
    searchUnitOptions.value = []
  }

  // ==================== 用人单位选择器方法 ====================
  
  /**
   * 用人单位选择器点击事件
   */
  const onSearchUnitClick = () => {
    selectedSearchUnitValues.value = [selectedSearchUnitKey.value]
    searchUnitVisible.value = true
  }
  
  /**
   * 用人单位选择确认
   * @param {Object} param - 选择结果
   * @param {Array} param.selectedOptions - 选中的选项
   */
  const onSearchUnitConfirm = ({ selectedOptions }) => {
    if (selectedOptions && selectedOptions.length > 0) {
      selectedSearchUnitText.value = selectedOptions[0].text
      selectedSearchUnitKey.value = selectedSearchUnitValues.value[0]
    } else {
      selectedSearchUnitText.value = "全部单位"
      selectedSearchUnitKey.value = ""
    }
    searchUnitVisible.value = false
  }
  
  /**
   * 用人单位选择取消
   */
  const onSearchUnitCancel = () => {
    searchUnitVisible.value = false
  }

  // ==================== 排序方法 ====================
  
  /**
   * 切换排序方式
   * @param {string} order - 排序方式 'asc' | 'desc'
   */
  const changeSortOrder = (order) => {
    if (sortOrder.value !== order) {
      sortOrder.value = order
      
      // 如果已经搜索过，重新执行搜索以应用新的排序
      if (hasSearched.value) {
        executeSearch({ sort_order: order })
      }
    }
  }
  
  /**
   * 获取当前排序参数
   */
  const getSortParams = () => {
    return {
      sort_order: sortOrder.value
    }
  }

  // ==================== 工具方法 ====================
  
  /**
   * 设置搜索条件（用于从外部设置搜索状态）
   * @param {Object} conditions - 搜索条件
   * @param {string} conditions.positionName - 职位名称
   * @param {string} conditions.unitId - 单位ID
   * @param {string} conditions.unitName - 单位名称
   */
  const setSearchConditions = (conditions = {}) => {
    if (conditions.positionName !== undefined) {
      positionInput.value = conditions.positionName
    }
    
    if (conditions.unitId !== undefined) {
      selectedSearchUnitKey.value = conditions.unitId
    }
    
    if (conditions.unitName !== undefined) {
      selectedSearchUnitText.value = conditions.unitName || "全部单位"
    }
    
    hasSearched.value = !!(conditions.positionName || conditions.unitId)
  }
  
  /**
   * 获取当前搜索条件的描述文本
   */
  const getSearchDescription = () => {
    const descriptions = []
    
    if (positionInput.value.trim()) {
      descriptions.push(`职位：${positionInput.value.trim()}`)
    }
    
    if (selectedSearchUnitKey.value && selectedSearchUnitText.value !== "全部单位") {
      descriptions.push(`单位：${selectedSearchUnitText.value}`)
    }
    
    return descriptions.length > 0 ? descriptions.join('，') : '全部职位'
  }

  // ==================== 返回值 ====================
  return {
    // 搜索表单状态
    positionInput,
    selectedSearchUnitText,
    selectedSearchUnitKey,
    searchUnitOptions,
    searchUnitVisible,
    selectedSearchUnitValues,
    hasSearched,
    isSearching,
    sortOrder,
    
    // 计算属性
    hasSearchConditions,
    searchParams,
    
    // 数据获取方法
    getWorkList,
    
    // 搜索方法
    executeSearch,
    onSearchClick,
    onClearSearch,
    resetSearchForm,
    
    // 用人单位选择器方法
    onSearchUnitClick,
    onSearchUnitConfirm,
    onSearchUnitCancel,
    
    // 排序方法
    changeSortOrder,
    getSortParams,
    
    // 工具方法
    setSearchConditions,
    getSearchDescription
  }
}
