<template>
  <div class="chart-section" v-if="chartData && chartData.length > 1">
    <div class="chart-title">{{ title }}</div>
    <div class="chart-scroll-container">
      <div ref="chartContainer" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, onUnmounted } from "vue"
import * as echarts from "echarts"

// Props
const props = defineProps({
  title: {
    type: String,
    default: "数据图表",
  },
  chartData: {
    type: Array,
    default: () => [],
  },
  dataKey: {
    type: String,
    default: "value", // 数据字段名，如 'num', 'min_min_score' 等
  },
  nameKey: {
    type: String,
    default: "name", // 名称字段名，如 'area_name' 等
  },
  chartType: {
    type: String,
    default: "bar", // 图表类型
  },
  tooltipFormatter: {
    type: Function,
    default: null, // 自定义tooltip格式化函数
  },
  formatNumber: {
    type: Function,
    default: (num) => num, // 数字格式化函数
  },
})

const chartContainer = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartContainer.value || !props.chartData || props.chartData.length === 0) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartContainer.value)

  // 处理数据，跳过第一个"全部"项
  const chartData = props.chartData.slice(1)

  if (chartData.length === 0) {
    // 当没有数据时，显示空图表
    chartInstance.setOption({
      backgroundColor: "#ffffff",
      title: { show: false },
      grid: {
        left: 0,
        right: 20,
        top: 30,
        bottom: 30,
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: [],
        axisLine: { lineStyle: { color: "#E5E5E5" } },
        axisTick: { show: false },
        axisLabel: { color: "#999", fontSize: 12, interval: 0 },
      },
      yAxis: {
        type: "value",
        min: 0,
        splitLine: { lineStyle: { color: "#E5E5E5", type: "dashed" } },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: "#999",
          fontSize: 12,
          margin: 20,
          formatter: function (value) {
            return value.toLocaleString()
          },
        },
      },
      series: [{ type: "bar", data: [] }],
    })
    return
  }

  const option = {
    backgroundColor: "#ffffff",
    title: { show: false },
    tooltip: {
      trigger: "item",
      confine: true,
      backgroundColor: "#FFFFFF",
      borderColor: "#E1E3E5",
      borderWidth: 1,
      padding: [8, 12],
      textStyle: { color: "#3C3D42", fontSize: 11 },
      formatter:
        props.tooltipFormatter ||
        function (params) {
          const data = chartData[params.dataIndex]
          return `<div style="font-size: 0.32rem; color: #3C3D42; margin-bottom: 8px;">${data[props.nameKey]}</div>
                <div style="font-size: 0.29rem; color: #919499;">
                  <span style="width: 1.2rem;margin-right: 0.2rem;">数值</span>
                  <span style="color: #3C3D42; font-weight: 400;">${props.formatNumber(data[props.dataKey]) || 0}</span>
                </div>`
        },
    },
    grid: {
      left: 0,
      right: 20,
      top: 30,
      bottom: 30,
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.map((item) => item[props.nameKey]),
      axisLine: { lineStyle: { color: "#E5E5E5" } },
      axisTick: { show: false },
      axisLabel: { color: "#999", fontSize: 12, interval: 0 },
    },
    yAxis: {
      type: "value",
      min: 0,
      splitLine: { lineStyle: { color: "#E5E5E5", type: "dashed" } },
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: "#999",
        fontSize: 12,
        margin: 20,
        formatter: function (value) {
          return value.toLocaleString()
        },
      },
      nameGap: 30,
      offset: 0,
      splitNumber: 4,
      boundaryGap: ["10%", "30%"],
    },
    series: [
      {
        type: props.chartType,
        barWidth: chartData.length <= 6 ? "40%" : 25,
        barCategoryGap: chartData.length <= 6 ? "20%" : "35%",
        data: chartData.map((item) => item[props.dataKey] || 0),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(248, 78, 78, 1)" },
            { offset: 1, color: "rgba(248, 78, 78, 0.60)" },
          ]),
          borderRadius: [6, 6, 0, 0],
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(248, 78, 78, 1)" },
              { offset: 1, color: "rgba(248, 78, 78, 0.80)" },
            ]),
          },
        },
        barMinHeight: 5,
      },
    ],
  }

  chartInstance.setOption(option)
}

// 重新初始化图表
const reinitChart = () => {
  nextTick(() => {
    // 动态计算图表宽度
    const dataLength = props.chartData?.length || 1
    const chartContainerEl = chartContainer.value

    if (chartContainerEl) {
      if (dataLength > 0) {
        if (dataLength <= 6) {
          chartContainerEl.style.width = "100%"
        } else {
          const itemWidth = 100 / 75 // 100rpx -> rem
          const marginWidth = 80 / 75 // 80rpx -> rem
          chartContainerEl.style.width = `${dataLength * itemWidth + marginWidth}rem`
        }
      } else {
        chartContainerEl.style.width = "100%"
      }

      initChart()
    }
  })
}

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    reinitChart()
  },
  { deep: true }
)

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  reinitChart()
  window.addEventListener("resize", handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener("resize", handleResize)
})
</script>

<style lang="scss" scoped>
.chart-section {
  background: #ffffff;
  padding: 0.53rem 0 0.53rem 0.43rem;
  margin-bottom: 0.213rem;

  .chart-title {
    font-size: 0.427rem;
    color: #22242e;
    font-weight: bold;
    margin-bottom: 0.32rem;
  }

  .chart-scroll-container {
    width: 100%;
    overflow-x: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .chart-container {
    height: 5rem;
    min-width: 100%;
  }
}
</style>
