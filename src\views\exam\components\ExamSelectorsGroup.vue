<template>
  <div class="exam-selectors-group">
    <!-- 选择器区域 -->
    <!-- 平板布局：三个选择器在同一行 -->
    <div v-if="isTablet" class="select-tablet">
      <div class="select-item" @click="onExamTypeClick">
        <div class="text">{{ selectedExamTypeText }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
      <div class="select-item" @click="onRegionClick">
        <div class="text">{{ selectedRegionText }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
      <div class="select-item" @click="onSpecificExamClick">
        <div class="text">{{ projectName }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
    </div>

    <!-- 手机布局：原有的2+1布局 -->
    <template v-else>
      <div class="select-one">
        <div class="select-item" @click="onExamTypeClick">
          <div class="text">{{ selectedExamTypeText }}</div>
          <img
            class="arrow"
            src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
          />
        </div>
        <div class="select-item" @click="onRegionClick">
          <div class="text">{{ selectedRegionText }}</div>
          <img
            class="arrow"
            src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
          />
        </div>
      </div>

      <div class="select-item w100" @click="onSpecificExamClick">
        <div class="text">{{ projectName }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
    </template>

    <!-- 选择器弹窗 -->
    <van-popup v-model:show="examTypeVisible" position="bottom">
      <van-picker
        v-model="selectedExamValues"
        :columns="examTypeOptions"
        @confirm="onExamTypeConfirm"
        @cancel="examTypeVisible = false"
      />
    </van-popup>

    <van-popup v-model:show="regionVisible" position="bottom">
      <van-picker
        v-model="selectedRegionValues"
        title="请选择所属地区"
        :columns="regionOptions"
        @confirm="onRegionConfirm"
        @cancel="regionVisible = false"
      />
    </van-popup>

    <van-popup v-model:show="specificExamVisible" position="bottom">
      <van-picker
        :columns="specificExamOptions"
        @confirm="onSpecificExamConfirm"
        @cancel="specificExamVisible = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { computed } from "vue"

/**
 * 考试选择器组件
 *
 * 封装考试类型、地区、具体考试选择器的UI和交互逻辑
 * 支持平板和手机两种布局模式
 */

// Props定义
const props = defineProps({
  // 平板模式标识
  isTablet: {
    type: Boolean,
    default: false,
  },

  // 选择器状态
  examTypeVisible: {
    type: Boolean,
    default: false,
  },
  regionVisible: {
    type: Boolean,
    default: false,
  },
  specificExamVisible: {
    type: Boolean,
    default: false,
  },

  // 选择器选项数据
  examTypeOptions: {
    type: Array,
    default: () => [],
  },
  regionOptions: {
    type: Array,
    default: () => [],
  },
  specificExamOptions: {
    type: Array,
    default: () => [],
  },

  // 选中的值
  selectedExamValues: {
    type: Array,
    default: () => [],
  },
  selectedRegionValues: {
    type: Array,
    default: () => [],
  },

  // 显示文本
  selectedExamTypeText: {
    type: String,
    default: "",
  },
  selectedRegionText: {
    type: String,
    default: "",
  },
  projectName: {
    type: String,
    default: "",
  },

  // 交互方法
  onExamTypeClick: {
    type: Function,
    default: () => {},
  },
  onRegionClick: {
    type: Function,
    default: () => {},
  },
  onSpecificExamClick: {
    type: Function,
    default: () => {},
  },
  onExamTypeConfirm: {
    type: Function,
    default: () => {},
  },
  onRegionConfirm: {
    type: Function,
    default: () => {},
  },
  onSpecificExamConfirm: {
    type: Function,
    default: () => {},
  },
})

// 为了支持v-model，需要定义emits
const emit = defineEmits([
  "update:examTypeVisible",
  "update:regionVisible",
  "update:specificExamVisible",
  "update:selectedExamValues",
  "update:selectedRegionValues",
])

// 响应式引用，用于双向绑定
const examTypeVisible = computed({
  get: () => props.examTypeVisible,
  set: (value) => emit("update:examTypeVisible", value),
})

const regionVisible = computed({
  get: () => props.regionVisible,
  set: (value) => emit("update:regionVisible", value),
})

const specificExamVisible = computed({
  get: () => props.specificExamVisible,
  set: (value) => emit("update:specificExamVisible", value),
})

const selectedExamValues = computed({
  get: () => props.selectedExamValues,
  set: (value) => emit("update:selectedExamValues", value),
})

const selectedRegionValues = computed({
  get: () => props.selectedRegionValues,
  set: (value) => emit("update:selectedRegionValues", value),
})
</script>

<style lang="scss" scoped>
.exam-selectors-group {
  width: 100%;

  // 平板布局的选择器样式
  .select-tablet {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.35rem;

    .select-item {
      flex: 1;
      height: 1.17rem;
      background: #ffffff;
      border-radius: 0.16rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0.43rem;
      box-sizing: border-box;

      .text {
        font-size: 0.37rem;
        font-weight: 400;
        color: #333333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .arrow {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.21rem;
        flex-shrink: 0;
      }
    }
  }

  // 手机布局的选择器样式
  .select-one {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.35rem;
    margin-bottom: 0.35rem;

    .select-item {
      flex: 1;
      height: 1.17rem;
      background: #ffffff;
      border-radius: 0.16rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0.43rem;
      box-sizing: border-box;

      .text {
        font-size: 0.37rem;
        font-weight: 400;
        color: #333333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .arrow {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.21rem;
        flex-shrink: 0;
      }
    }
  }

  .select-item.w100 {
    width: 100%;
    height: 1.17rem;
    background: #ffffff;
    border-radius: 0.16rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.43rem;
    box-sizing: border-box;

    .text {
      font-size: 0.37rem;
      font-weight: 400;
      color: #333333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .arrow {
      width: 0.32rem;
      height: 0.32rem;
      margin-left: 0.21rem;
      flex-shrink: 0;
    }
  }
}

// 弹窗样式
:deep(.van-popup) {
  border-radius: 0.32rem 0.32rem 0 0;
}

:deep(.van-picker__title) {
  font-size: 0.43rem;
  color: #313436;
}

:deep(.van-picker__cancel) {
  color: #c2c5cc;
  font-size: 0.37rem;
}

:deep(.van-picker__confirm) {
  color: #e60003;
  font-size: 0.37rem;
}

:deep(.van-picker-column__item) {
  font-size: 0.373rem;
  font-weight: 400;
  color: #999999;
}

:deep(.van-picker-column__item--selected) {
  color: #333333;
}
</style>
