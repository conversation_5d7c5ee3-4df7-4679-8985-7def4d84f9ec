/**
 * APP环境管理Hook
 * 
 * 封装APP环境检测、页面操作、高度计算等通用逻辑
 * 适用于需要APP环境适配的页面
 * 
 * @param {Object} options - 配置选项
 * @param {Function} options.isDeviceAppClientCookie - 检测是否在APP环境的函数
 * @param {Function} options.closeAppWebview - 关闭APP webview的函数
 * @param {Function} options.shareAppPage - APP分享页面的函数
 * @param {Function} options.openAppUrl - 打开APP URL的函数
 * @param {Object} options.routeParams - 路由参数对象
 * 
 * @returns {Object} 返回APP环境相关的状态和方法
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

export function useAppEnvironment(options = {}) {
  const {
    isDeviceAppClientCookie,
    closeAppWebview,
    shareAppPage,
    openAppUrl,
    routeParams = {}
  } = options

  // ==================== APP环境状态 ====================
  
  // APP环境检测
  const isInAPP = computed(() => {
    return isDeviceAppClientCookie ? isDeviceAppClientCookie() : false
  })
  
  // 页面高度相关
  const topHeight = ref(null)
  const topShow = ref(false)
  
  // 屏幕尺寸相关
  const isTablet = ref(false)
  const screenWidth = ref(0)
  const screenHeight = ref(0)
  
  // 滚动相关
  const scrollPositions = ref({})
  const isTabSticky = ref(false)
  const stickyThreshold = ref(100) // 吸顶阈值

  // ==================== 高度计算方法 ====================
  
  /**
   * 获取页面高度
   * @returns {number} 页面高度
   */
  const getHeight = () => {
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.clientHeight
    return Math.max(windowHeight, documentHeight)
  }
  
  /**
   * 获取导航栏高度
   * @param {number} num - 额外的高度偏移
   * @returns {number} 导航栏高度
   */
  const getNavigationBarHeight = (num = 0) => {
    if (isInAPP.value) {
      // APP环境下的导航栏高度计算
      const statusBarHeight = 44 // 状态栏高度
      const navBarHeight = 44 // 导航栏高度
      return statusBarHeight + navBarHeight + num
    } else {
      // 浏览器环境下的导航栏高度
      return 44 + num
    }
  }
  
  /**
   * 计算内容区域高度
   * @param {number} excludeHeight - 需要排除的高度
   * @returns {number} 内容区域高度
   */
  const getContentHeight = (excludeHeight = 0) => {
    const totalHeight = getHeight()
    const navHeight = getNavigationBarHeight()
    return totalHeight - navHeight - excludeHeight
  }

  // ==================== 屏幕尺寸检测 ====================
  
  /**
   * 检查屏幕尺寸
   */
  const checkScreenSize = () => {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
    
    // 判断是否为平板设备（宽度大于768px）
    isTablet.value = screenWidth.value > 768
  }
  
  /**
   * 窗口大小变化处理
   */
  const handleResize = () => {
    checkScreenSize()
    
    // 重新计算高度
    nextTick(() => {
      topHeight.value = getHeight()
    })
  }

  // ==================== 滚动处理 ====================
  
  /**
   * 处理页面滚动
   * @param {Event} event - 滚动事件
   */
  const handleScroll = (event) => {
    const scrollTop = event.target.scrollTop || window.pageYOffset
    
    // 更新吸顶状态
    isTabSticky.value = scrollTop > stickyThreshold.value
    
    // 更新顶部显示状态
    topShow.value = scrollTop > 100
  }
  
  /**
   * 保存滚动位置
   * @param {string} key - 滚动位置的键名
   * @param {number} position - 滚动位置
   */
  const saveScrollPosition = (key, position) => {
    scrollPositions.value[key] = position
  }
  
  /**
   * 恢复滚动位置
   * @param {string} key - 滚动位置的键名
   * @returns {number} 滚动位置
   */
  const restoreScrollPosition = (key) => {
    return scrollPositions.value[key] || 0
  }

  // ==================== APP操作方法 ====================
  
  /**
   * 返回上一页
   */
  const backPage = () => {
    if (isInAPP.value && closeAppWebview) {
      closeAppWebview()
    } else {
      // 浏览器环境下的返回逻辑
      if (window.history.length > 1) {
        window.history.back()
      } else {
        // 如果没有历史记录，跳转到首页或其他默认页面
        window.location.href = '/'
      }
    }
  }
  
  /**
   * 分享页面
   * @param {Object} shareInfo - 分享信息
   * @param {string} shareInfo.title - 分享标题
   * @param {string} shareInfo.desc - 分享描述
   * @param {string} shareInfo.url - 分享链接
   * @param {string} shareInfo.image - 分享图片
   */
  const sharePage = (shareInfo = {}) => {
    if (isInAPP.value && shareAppPage) {
      shareAppPage(shareInfo)
    } else {
      // 浏览器环境下的分享逻辑
      if (navigator.share) {
        navigator.share({
          title: shareInfo.title,
          text: shareInfo.desc,
          url: shareInfo.url
        }).catch(err => {
          console.log('分享失败:', err)
          // 降级到复制链接
          copyToClipboard(shareInfo.url)
        })
      } else {
        // 降级到复制链接
        copyToClipboard(shareInfo.url)
      }
    }
  }
  
  /**
   * 打开APP内部URL
   * @param {string} type - URL类型
   * @param {Object} params - URL参数
   */
  const goAppUrl = (type, params = {}) => {
    if (isInAPP.value && openAppUrl) {
      openAppUrl(type, params)
    } else {
      console.warn('goAppUrl only works in APP environment')
    }
  }
  
  /**
   * 跳转到职位详情
   * @param {Object} jobInfo - 职位信息
   * @param {string|number} jobInfo.id - 职位ID
   * @param {string} jobInfo.name - 职位名称
   */
  const goJobDetail = (jobInfo) => {
    if (isInAPP.value) {
      goAppUrl('job_detail', {
        job_id: jobInfo.id,
        job_name: jobInfo.name,
        ...routeParams
      })
    } else {
      // 浏览器环境下的跳转逻辑
      const url = `/job/detail/${jobInfo.id}`
      window.open(url, '_blank')
    }
  }

  // ==================== 工具方法 ====================
  
  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   */
  const copyToClipboard = (text) => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(() => {
        console.log('链接已复制到剪贴板')
      }).catch(err => {
        console.error('复制失败:', err)
      })
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        console.log('链接已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
      }
      document.body.removeChild(textArea)
    }
  }
  
  /**
   * 设置吸顶阈值
   * @param {number} threshold - 阈值
   */
  const setStickyThreshold = (threshold) => {
    stickyThreshold.value = threshold
  }

  // ==================== 生命周期 ====================
  
  onMounted(() => {
    // 初始化屏幕尺寸检测
    checkScreenSize()
    
    // 初始化高度
    nextTick(() => {
      topHeight.value = getHeight()
    })
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
  })
  
  onUnmounted(() => {
    // 移除事件监听
    window.removeEventListener('resize', handleResize)
  })

  // ==================== 返回值 ====================
  return {
    // APP环境状态
    isInAPP,
    topHeight,
    topShow,
    isTablet,
    screenWidth,
    screenHeight,
    
    // 滚动相关
    scrollPositions,
    isTabSticky,
    stickyThreshold,
    
    // 高度计算方法
    getHeight,
    getNavigationBarHeight,
    getContentHeight,
    
    // 屏幕尺寸方法
    checkScreenSize,
    handleResize,
    
    // 滚动处理方法
    handleScroll,
    saveScrollPosition,
    restoreScrollPosition,
    setStickyThreshold,
    
    // APP操作方法
    backPage,
    sharePage,
    goAppUrl,
    goJobDetail,
    
    // 工具方法
    copyToClipboard
  }
}
